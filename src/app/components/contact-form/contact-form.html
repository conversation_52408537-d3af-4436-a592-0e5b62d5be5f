<div class="surface">
  <span class="detail-header">
    <mat-icon
      class="header-icon"
      svgIcon="arrow_back"
      aria-hidden="false"
      aria-label="Back Icon"
      style="cursor: pointer"
      [routerLink]="['/offender-detail', offender?.ofndrNum]"
    ></mat-icon>
    <span class="title_large">Supervision Contact</span>
  </span>
  <div class="body_large">
    Supervision Contact entry for
    <strong>{{offender?.firstName}} {{offender?.lastName}}</strong>.
  </div>

  <form [formGroup]="contactForm" (submit)="onSubmit()">
    <ng-template #loading>Loading...</ng-template>
    <mat-form-field class="contact_form_input_field">
      <mat-label>Choose a date</mat-label>
      <input
        matInput
        [matDatepicker]="picker"
        [formControl]="dateTimeControl"
        placeholder="MM/DD/YYYY"
        aria-label="Contact Date"
        required
        formControlName="contactDate"
      />
      <mat-hint style="font-size: 10px; line-height: 0px; margin-top: 0px"
        >MM/DD/YYYY</mat-hint
      >
      <mat-datepicker-toggle
        matIconSuffix
        [for]="picker"
      ></mat-datepicker-toggle>
      <mat-datepicker #picker></mat-datepicker>
    </mat-form-field>

    <mat-form-field class="contact_form_input_field">
      <mat-label>Pick a time</mat-label>
      <input
        matInput
        [formControl]="dateTimeControl"
        [matTimepicker]="timePicker"
        formControlName="contactTime"
      />
      <mat-timepicker-toggle matIconSuffix [for]="timePicker" />
      <mat-timepicker #timePicker format="24" />
    </mat-form-field>

    <mat-form-field class="contact_form_input_field">
      <mat-label>Primary Interviewer</mat-label>
      <mat-select [formControl]="primaryInterviewerControl">
        <mat-option>
          <input matInput formControlName="primaryInterviewer" />
        </mat-option>
        <ng-container *ngIf="primaryOfficers | async as options; else loading">
          <mat-option
            *ngFor="let option of options; trackBy: trackOption"
            [value]="option.id"
          >
            {{option.text}}
          </mat-option>
        </ng-container>
      </mat-select>
    </mat-form-field>

    <mat-form-field class="contact_form_input_field">
      <mat-label>Secondary Interviewer</mat-label>
      <mat-select [formControl]="secondaryInterviewerControl">
        <mat-option>
          <input matInput formControlName="secondaryInterviewer" />
        </mat-option>
        <ng-container
          *ngIf="secondaryOfficers | async as options; else loading"
        >
          <mat-option
            *ngFor="let option of options; trackBy: trackOption"
            [value]="option.id"
          >
            {{option.text}}
          </mat-option>
        </ng-container>
      </mat-select>
    </mat-form-field>

    <!-- <mat-form-field class="contact_form_input_field">
      <mat-label>Secondary Interviewer</mat-label>
      <mat-select [formControl]="secondaryInterviewerControl">
        <mat-option>
          <input matInput formControlName="secondaryInterviewer" />
        </mat-option>
        <ng-container *ngIf="secondaryInterviewerOptions | async as options; else loading">
          <mat-option
            *ngFor="let option of options; trackBy: trackOption"
            [value]="option.id"
          >
            {{option.text}}
          </mat-option>
        </ng-container>
      </mat-select>
    </mat-form-field> -->
    <mat-form-field class="contact_form_input_field">
      <mat-label>Contact Type</mat-label>
      <mat-select required [formControl]="contactTypeControl">
        <mat-option>
          <input matInput formControlName="contactType" />
        </mat-option>
        <mat-option
          *ngFor="let option of contactTypeOptions | async; trackBy: trackOption"
          [value]="option.id"
          >{{option.text}}</mat-option
        >
      </mat-select>
    </mat-form-field>

    <mat-form-field class="contact_form_input_field">
      <mat-label>Location</mat-label>
      <mat-select [formControl]="contactLocationControl" required>
        <mat-option>
          <input matInput formControlName="location" />
        </mat-option>
        <mat-option
          *ngFor="let option of locationOptions | async; trackBy: trackOption"
          [value]="option.id"
          >{{option.text}}</mat-option
        >
      </mat-select>
    </mat-form-field>

    <div>
      <mat-divider style="color: lightgray"></mat-divider>
      <section>
        <div class="button-row">
          <button
            matButton="outlined"
            [routerLink]="['/offender-detail', offender?.ofndrNum]"
          >
            Cancel
          </button>
          <button
            matButton="filled"
            type="submit"
            (click)="onSubmit()"
            [routerLink]="['/commentary-form', currentContact.ofndrNum, currentContact.contactId]"
            [disabled]="!contactForm.valid"
          >
            Next
          </button>
        </div>
      </section>
    </div>
  </form>
</div>
