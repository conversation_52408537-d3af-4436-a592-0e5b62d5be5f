import { ComponentFixture, TestBed } from '@angular/core/testing';\nimport { RouterTestingModule } from '@angular/router/testing';\nimport { HttpClientTestingModule } from '@angular/common/http/testing';\nimport { NoopAnimationsModule } from '@angular/platform-browser/animations';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { ActivatedRoute } from '@angular/router';\n\nimport { CommentaryForm } from './commentary-form';\nimport { ContactData } from '../../services/contact-data';\nimport { Navigation } from '../../services/navigation';\nimport { Contact } from '../../model/Contact';\nimport { Offender } from '../../model/Offender';\n\ndescribe('CommentaryForm', () => {\n  let component: CommentaryForm;\n  let fixture: ComponentFixture<CommentaryForm>;\n  let mockContactData: jasmine.SpyObj<ContactData>;\n  let mockNavigation: jasmine.SpyObj<Navigation>;\n\n  const mockContact: Contact = {\n    contactId: 1,\n    ofndrNum: 12345,\n    agentId: 'agent1',\n    secondaryAgentId: '',\n    contactDate: new Date('2024-01-01'),\n    contactType: '1',\n    contactTypeDesc: 'Office Visit',\n    location: '1',\n    locationDesc: 'Main Office',\n    commentary: '',\n    formCompleted: false,\n    firstPageCompleted: true,\n    wasContactSuccessful: false\n  };\n\n  const mockOffender: Offender = {\n    ofndrNum: 12345,\n    firstName: 'Jane',\n    lastName: 'Smith',\n    birthDate: new Date('1990-01-01'),\n    image: 'offender1.jpg',\n    address: '456 Oak St',\n    city: 'Anytown',\n    state: 'ST',\n    zip: '12345',\n    phone: '555-0123',\n    lastSuccessfulContactDate: new Date('2024-01-01'),\n    contactArray: []\n  };\n\n  beforeEach(async () => {\n    const contactDataSpy = jasmine.createSpyObj('ContactData', [\n      'getContactById',\n      'getCaseloadOffenderById',\n      'getOtherOffendersOffenderById',\n      'updateContact'\n    ]);\n\n    const navigationSpy = jasmine.createSpyObj('Navigation', ['navigate']);\n\n    await TestBed.configureTestingModule({\n      imports: [\n        CommentaryForm,\n        RouterTestingModule,\n        HttpClientTestingModule,\n        NoopAnimationsModule,\n        ReactiveFormsModule\n      ],\n      providers: [\n        { provide: ContactData, useValue: contactDataSpy },\n        { provide: Navigation, useValue: navigationSpy },\n        {\n          provide: ActivatedRoute,\n          useValue: {\n            snapshot: {\n              params: { ofndrNum: '12345', contactId: '1' }\n            }\n          }\n        }\n      ]\n    }).compileComponents();\n\n    mockContactData = TestBed.inject(ContactData) as jasmine.SpyObj<ContactData>;\n    mockNavigation = TestBed.inject(Navigation) as jasmine.SpyObj<Navigation>;\n\n    // Setup default mock returns\n    mockContactData.getContactById.and.returnValue(Promise.resolve(mockContact));\n    mockContactData.getCaseloadOffenderById.and.returnValue(Promise.resolve(mockOffender));\n    mockContactData.getOtherOffendersOffenderById.and.returnValue(Promise.resolve(undefined));\n    mockContactData.updateContact.and.returnValue(Promise.resolve());\n\n    fixture = TestBed.createComponent(CommentaryForm);\n    component = fixture.componentInstance;\n  });\n\n  it('should create', () => {\n    expect(component).toBeTruthy();\n  });\n\n  it('should initialize with route parameters', async () => {\n    await component.ngOnInit();\n    \n    expect(component.ofndrNum).toBe(12345);\n    expect(component.contactId).toBe(1);\n  });\n\n  it('should load contact and offender data on initialization', async () => {\n    await component.ngOnInit();\n    \n    expect(mockContactData.getContactById).toHaveBeenCalledWith(1);\n    expect(mockContactData.getCaseloadOffenderById).toHaveBeenCalledWith(12345);\n    expect(component.currentContact).toEqual(mockContact);\n    expect(component.offender).toEqual(mockOffender);\n  });\n\n  it('should fallback to other offenders if not in caseload', async () => {\n    mockContactData.getCaseloadOffenderById.and.returnValue(Promise.resolve(undefined));\n    mockContactData.getOtherOffendersOffenderById.and.returnValue(Promise.resolve(mockOffender));\n\n    await component.ngOnInit();\n\n    expect(mockContactData.getOtherOffendersOffenderById).toHaveBeenCalledWith(12345);\n    expect(component.offender).toEqual(mockOffender);\n  });\n\n  it('should handle form submission and update contact', async () => {\n    await component.ngOnInit();\n    \n    // Simulate form data\n    const commentaryText = 'Test commentary for the contact';\n    const wasSuccessful = true;\n    \n    // Mock form submission\n    component.currentContact.commentary = commentaryText;\n    component.currentContact.wasContactSuccessful = wasSuccessful;\n    component.currentContact.formCompleted = true;\n\n    await component.onSubmit();\n\n    expect(mockContactData.updateContact).toHaveBeenCalledWith(jasmine.objectContaining({\n      contactId: 1,\n      commentary: commentaryText,\n      wasContactSuccessful: wasSuccessful,\n      formCompleted: true\n    }));\n  });\n\n  it('should handle missing contact gracefully', async () => {\n    mockContactData.getContactById.and.returnValue(Promise.resolve(undefined));\n\n    await component.ngOnInit();\n\n    // Should handle undefined contact without throwing\n    expect(component.currentContact).toBeDefined();\n  });\n\n  it('should handle missing offender gracefully', async () => {\n    mockContactData.getCaseloadOffenderById.and.returnValue(Promise.resolve(undefined));\n    mockContactData.getOtherOffendersOffenderById.and.returnValue(Promise.resolve(undefined));\n\n    await component.ngOnInit();\n\n    // Should handle undefined offender without throwing\n    expect(component.offender).toBeUndefined();\n  });\n});