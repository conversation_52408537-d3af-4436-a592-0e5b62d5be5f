<div class="surface">
  <span class="detail-header">
    <mat-icon
      class="header-icon"
      svgIcon="arrow_back"
      aria-hidden="false"
      aria-label="Back Icon"
      style="cursor: pointer"
      [routerLink]="['/contact-form', (currentContact | async)?.ofndrNum, (currentContact | async)?.contactId]"
    ></mat-icon>
    <span class="title_large">Add Commentary</span>
  </span>
  <div class="surface-container-low-outlined">
    <div class="contact-info-container">
      <span class="label_medium">Offender</span>
      <span class="body_large"
        >{{offender?.firstName}} {{offender?.lastName}}</span
      >
    </div>
    <div class="contact-info-container">
      <span class="label_medium">Date</span>
      <span class="body_large"
        >{{(currentContact | async)?.contactDate | date: 'MM/dd/yyyy'}} |
        {{(currentContact | async)?.contactDate | date: 'HH:mm'}}</span
      >
    </div>
    <div class="contact-info-container">
      <span class="label_medium">Primary Interviewer</span>
      <span class="body_large"
        >{{(primaryAgent | async)?.fullName || 'Refresh Data'}}</span
      >
    </div>
    <div class="contact-info-container">
      <span class="label_medium">Secondary Interviewer</span>
      <span class="body_large">
        {{(secondaryAgent | async)?.fullName || 'N/A'}}
      </span>
    </div>
    <div class="contact-info-container">
      <span class="label_medium">Contact Type</span>
      <span class="body_large">
        <ng-container *ngIf="(currentContact | async) as contact">
          {{ contactTypeName | async}}
        </ng-container>
      </span>
    </div>
    <div class="contact-info-container">
      <span class="label_medium">Location</span>
      <span class="body_large">
        <ng-container *ngIf="(currentContact | async) as contact">
          {{ locationName | async }}
        </ng-container>
      </span>
    </div>
  </div>
  <div
    class="type-of-contact-btn-container"
    *ngIf="(currentContact | async) as contact"
  ></div>
  <div class="summary-of-contact-container">
    <form [formGroup]="commentaryForm" (submit)="onSubmit()">
      <mat-button-toggle-group
        formControlName="wasContactSuccessful"
        required
        class="{{errorClass}}"
        name="fontStyle"
        aria-label="Font Style"
      >
        <mat-button-toggle
          class="successFab"
          matFab
          (click)="madeContact()"
          value="bold"
          >Made Contact</mat-button-toggle
        >
        <mat-button-toggle
          class="successFab"
          matFab
          (click)="attemptedContact()"
          value="italic"
          >Attempted Contact</mat-button-toggle
        >
      </mat-button-toggle-group>
      <mat-form-field class="contact_form_input_field">
        <mat-label>Summary of Contact</mat-label>
        <textarea
          matInput
          formControlName="commentary"
          placeholder="Include details related to the R&R efforts of the visit."
          [ngModel]="(currentContact | async)?.commentary || ''"
          required
        ></textarea>
      </mat-form-field>
    </form>
    <span class="summary-of-contact-container"
      >Tap the mic next to your keyboard to initiate voice to text</span
    >
  </div>

  <div class="on-surface-variant">
    <span>
      <button matButton="text" (click)="openBottomSheet()">
        <mat-icon class="info-field-icon">info_outline</mat-icon> Field Visit
        Guidelines
      </button>
    </span>
  </div>

  <div class="on-surface-variant">
    <mat-divider class="divider"></mat-divider>
    <section>
      <div class="button-row">
        <button
          matButton="outlined"
          routerLink="/offender-detail/{{(currentContact | async)?.ofndrNum}}"
        >
          Cancel
        </button>

        <button
          matButton="filled"
          type="submit"
          (click)="onSubmit()"
          [disabled]="!commentaryForm.valid"
        >
          Save
        </button>
      </div>
    </section>
  </div>
</div>
