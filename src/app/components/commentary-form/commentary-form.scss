.surface {
  background: var(--UDC-styles-sys-light-surface, #fcf8f7);
  display: flex;
//   width: 393px;
  height: 852px;
  min-height: 852px;
  padding: 32px 16px;
  flex-direction: column;
  align-items: flex-start;
}
.surface-container-low-outlined {
  display: flex;
  padding: 12px 4px;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  border-radius: 16px;
//   border: 1px solid var(--UDC-styles-sys-light-outline, #7a776e);
background: var(--md-sys-color-surface-container-low);
}
.contact-info-container {
  display: flex;
  height: 56px;
  min-height: 56px;
  flex-direction: column;
  justify-content: center;
  align-items: left;
  align-self: stretch;
  padding: 0 16px;
}
.type-of-contact-btn {
  display: flex;
  height: 32px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  border: 1px solid var(--UDC-styles-sys-light-outline-variant, #cbc6bc);
}

.type-of-contact-btn-container {
  display: flex;
  flex-direction: row;
  max-height: 60px;
  align-items: end;
  gap: 16px;
  flex: 1 0 0;
  align-self: stretch;
}
.mat-button-toggle-checked {
  text-align: center;

  /* UDC-styles/label/large */
  font-family: "Source Sans 3";
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  /* 142.857% */
  letter-spacing: 0.1px;
  border-radius: 8px;
  background-color: var(--UDC-styles-sys-light-secondary-container, #d9e5f1) !important;
}
.disabled-class {
  color: var(--UDC-styles-sys-light-on-surface-variant, #49473f);
  text-align: center;

  /* UDC-styles/label/large */
  font-family: "Source Sans 3";
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  /* 142.857% */
  letter-spacing: 0.1px;
  border-radius: 8px;
  border: 1px solid var(--UDC-styles-sys-light-outline-variant, #cbc6bc);
}
  .mat-mdc-unelevated-button{
      display:inline-flex;
      height:40px;
      flex-direction:column;
      margin-left: 4px;
      margin-right: 4px;
      justify-content:center;
      align-items:center;
      gap:8px;
      flex-shrink:0;
      border-radius:100px;
  }
.summary-of-contact-container {
  display: flex;
  padding: 4px 0px 4px 0;
  align-items: flex-start;
  flex: 1 0 0;
  align-self: stretch;
  flex-direction: column;
}
.summary-of-contact-container form {
  width: 100%;
}
.on-surface-variant {
  color: var(--md-sys-color-on-surface-variant);
  width: 100%;
  display: flex; 
  align-items: center
}
.on-surface-varian children {
  display: flex;
  align-items: center;
  align-self: stretch;
  gap: 4px;
}
.mat-mdc-extended-fab {
  display: inline-flex;
  height: 40px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  width: 200px
}
.successFab {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.mat-mdc-button:has(.material-icons,mat-icon,[matButtonIcon]) {
  display: block;
  gap: 8px;
  flex-shrink: 0;
  padding-left: 16px;
  padding-right: 16px;
  margin-right: 16px;
}
