<div class="surface">
  <mat-toolbar>
    <mat-toolbar-row>
      <span class="header-spacer">
        <div>
        </div>
      </span>
      <mat-icon
        class="header-icon"
        svgIcon="search"
        aria-hidden="false"
        aria-label="Search Icon"
      ></mat-icon>
      <mat-icon
        class="header-icon"
        svgIcon="bell"
        aria-hidden="false"
        aria-label="Notifications Icon"
      ></mat-icon>
      <div class="primary-container" [routerLink]="['/agent-profile']">
        <span class="initials">
          <div *ngIf="currentAgent | async; let agent">
            {{agent.firstName.charAt(0) + agent.lastName.charAt(0) }}
          </div>
        </span>
      </div>
    </mat-toolbar-row>
    <mat-toolbar-row>
      <h2
        class="flex-item headline_small"
      >
        Supervision Contacts
      </h2>
    </mat-toolbar-row>
  </mat-toolbar>
  <app-my-caseload> </app-my-caseload>
  <app-other-offenders-list></app-other-offenders-list>
</div>
