.surface-container-low {
    background-color: var(--md-sys-color-surface-container-low);
    color: var(--md-sys-color-on-surface);
    display: flex;
    padding: 16px;
    flex-direction: column;
    align-items: left;
    margin-right: 16px;
    margin-top: 0px;
    align-self: stretch;
    border-radius: 16px;
    // width: 100%;
    min-width: 340px;
}
.mat-icon {
    cursor: pointer;
}
.mat-mdc-text-field-wrapper {
    border: 1px;
    border-style: solid;
    border-color: black;
}