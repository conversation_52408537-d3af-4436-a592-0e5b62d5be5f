<div class="surface">
  <div class="surface-container-low">
    <div class="surface-container">
      <h2 class="flex-item headline_small">My Caseload</h2>
      <mat-divider style="color: lightgray"></mat-divider>
      <div
        class="on-surface-variant"
        style="display: flex; align-items: center"
      >
        <span class="source-sans"
          >{{ (myCaseload | async)?.length || 0 }} offenders</span
        >
        <span class="header-spacer"></span>
        <form [formGroup]="searchForm" (keyup)="filterCaseloadByOffenderInformation(searchForm.value.searchTerm)">
          <mat-form-field class="contact_form_input_field">
            <mat-label>Search by offender information</mat-label>
            <input matInput type="text" 
            formControlName="searchTerm" />
            <mat-icon *ngIf="searchForm.value.searchTerm" matSuffix matIconButton aria-label="Clear" (click)="resetSearch()" fontIcon="close"></mat-icon>
          </mat-form-field>
        </form>
        <span class="header-spacer"></span>
        <mat-icon
          class="header-icon"
          svgIcon="filter"
          aria-hidden="false"
          aria-label="Filter Icon"
        ></mat-icon>
      </div>
      <div>
        <div *ngIf="myCaseload | async as data; else loading">
          <app-offender-card
            *ngFor="let offender of (myCaseload | async)"
            [offender]="offender"
          ></app-offender-card>
        </div>
        <ng-template #loading>
          <div>Loading...</div>
        </ng-template>
      </div>
    </div>
  </div>
</div>
