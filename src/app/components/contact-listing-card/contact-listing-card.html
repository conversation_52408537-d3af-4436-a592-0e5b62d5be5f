<a class="visit_listing_card" style="display: flex; margin-top: 15px; text-decoration: none; color: inherit;"
   [routerLink]="['/contact-detail-view', contact.contactId]" routerLinkActive="router-link-active">
    <div style="flex: 0;">
        <mat-icon class="header-icon" svgIcon="note" aria-hidden="false" aria-label="Back Icon"
        style="cursor: pointer;"></mat-icon>
    </div>
    <div style="flex: 1;">
        <span class="label_medium">{{contact.contactDate | date: 'longDate'}} | {{contact.contactDate | date: 'HH:mm'}}</span>
        <div class="body_large">{{(this.contactData.getContactTypeById(contact.contactType) | async)?.text}}</div>
        <div class="body_medium">{{contact.commentary}}</div>
    </div>
</a>