<div class="surface">
  <div class="surface-container-low">
    <div class="surface-container">
      <h2 class="flex-item headline_small">Other Offenders
        <mat-icon
          class="header-icon ripple-container"
          svgIcon="add-black"
          aria-hidden="false"
          aria-label="Add Offender Icon"
          routerLink="/add-offender-to-other-offenders"
        ></mat-icon>
      </h2>
      <mat-divider style="color: lightgray"></mat-divider>
      <div
        class="on-surface-variant"
      >
        <span class="source-sans"
          >{{(otherOffenders | async)?.length}} offenders</span
        >
        <span class="header-spacer"></span>
        <form [formGroup]="searchForm" (keyup)="filterCaseloadByFirstAndLastNames(searchForm.value.searchTerm)">
          <mat-form-field class="contact_form_input_field">
            <mat-label>Search by offender name</mat-label>
            <input matInput type="text" 
            formControlName="searchTerm" />
            <mat-icon *ngIf="searchForm.value.searchTerm" matSuffix matIconButton aria-label="Clear" (click)="searchForm.reset()" fontIcon="close"></mat-icon>
          </mat-form-field>
        </form>
        <span class="header-spacer"></span>
        <mat-icon
          class="header-icon"
          svgIcon="filter"
          aria-hidden="false"
          aria-label="Filter Icon"
        ></mat-icon>
      </div>
      <div>
        <div *ngIf="otherOffenders | async as data; else loading">
          <app-offender-card
            *ngFor="let offender of data"
            [offender]="offender"
          ></app-offender-card>
        </div>
        <ng-template #loading>
          <div>Loading...</div>
        </ng-template>
      </div>
    </div>
  </div>
</div>
