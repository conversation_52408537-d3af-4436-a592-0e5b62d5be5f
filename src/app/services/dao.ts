import { Injectable } from '@angular/core';
import { Agent } from '../model/Agent';
import { Select2Model } from '../model/Select2Model';
import { Offender } from '../model/Offender';
import { OffenderBase } from '../model/OffenderBase';

@Injectable({
  providedIn: 'root',
})
export class Dao {
  public agent: Agent = {
    agentId: 'jshardlow',
    firstName: 'Jeff',
    lastName: 'Shardlow',
    fullName: '<PERSON>',
    email: '<EMAIL>',
    image: '',
    address: '',
    city: '',
    state: '',
    zip: '',
    supervisorId: 'kchunt',
    ofndrNumList: ([] = []),
  };

  public otherOffenders: Offender[] = [
    {
      ofndrNum: 21,
      firstName: 'Yogli',
      lastName: 'Beary',
      birthDate: new Date('1980-01-01'),
      agentId: 'mromney',
      image: '',
      address: '1234 Main St.',
      city: 'Salt Lake City',
      state: 'UT',
      zip: '84115',
      phone: '************',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
    },
  ];

  public myCaseload: Offender[] = [
    {
      ofndrNum: 1,
      firstName: 'Yogi',
      lastName: 'Bear',
      birthDate: new Date('1980-01-01'),
      agentId: 'jshardlow',
      image: '',
      address: '123 Main St.',
      city: 'Salt Lake City',
      state: 'UT',
      zip: '',
      phone: '',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
    },
    {
      ofndrNum: 2,
      firstName: 'Papa',
      lastName: 'Smurf',
      birthDate: new Date('1975-02-02'),
      agentId: 'jshardlow',
      image: '',
      address: '234 Main St.',
      city: 'Salt Lake City',
      state: 'UT',
      zip: '84101',
      phone: '************',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
    },
    {
      ofndrNum: 3,
      firstName: 'Sneezy',
      lastName: 'Dwarf',
      birthDate: new Date('1985-03-03'),
      agentId: 'jshardlow',
      image: '',
      address: '345 Center St.',
      city: 'Salt Lake City',
      state: 'UT',
      zip: '84101',
      phone: '************',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
    },
    {
      ofndrNum: 4,
      firstName: 'Sleepy',
      lastName: 'Dwarf',
      birthDate: new Date('1986-04-04'),
      agentId: 'jshardlow',
      image: '',
      address: '1234 West Temple',
      city: 'South Salt Lake City',
      state: 'UT',
      zip: '84115',
      phone: '************',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
    },
    {
      ofndrNum: 5,
      firstName: 'Smurfette',
      lastName: 'Smurf',
      birthDate: new Date('1992-05-05'),
      agentId: 'jshardlow',
      image: '',
      address: '9212 700 E.',
      city: 'Magna',
      state: 'UT',
      zip: '84111',
      phone: '************',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
    },
    {
      ofndrNum: 6,
      firstName: 'Dopey',
      lastName: 'Dwarf',
      birthDate: new Date('1987-06-06'),
      agentId: 'jshardlow',
      image: '',
      address: '67 American Ave.',
      city: 'Murray',
      state: 'UT',
      zip: '84123',
      phone: '************',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
    },
    {
      ofndrNum: 7,
      firstName: 'Grumpy',
      lastName: 'Dwarf',
      birthDate: new Date('1988-07-07'),
      agentId: 'jshardlow',
      image: '',
      address: '12324 Minuteman Dr.',
      city: 'Draper',
      state: 'UT',
      zip: '84044',
      phone: '************',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
    },
    {
      ofndrNum: 8,
      firstName: 'Bashful',
      lastName: 'Dwarf',
      birthDate: new Date('1989-08-08'),
      agentId: 'jshardlow',
      image: '',
      address: '8523 Redwood Rd.',
      city: 'South Jordan',
      state: 'UT',
      zip: '84088',
      phone: '************',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
    },
    {
      ofndrNum: 9,
      firstName: 'Happy',
      lastName: 'Dwarf',
      birthDate: new Date('1991-09-09'),
      agentId: 'jshardlow',
      image: '',
      address: '735 500W',
      city: 'Moab',
      state: 'UT',
      zip: '84452',
      phone: '************',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
    },
    {
      ofndrNum: 10,
      firstName: 'Snow',
      lastName: 'White',
      birthDate: new Date('1990-01-01'),
      agentId: 'jshardlow',
      image: '',
      address: '345 State St.',
      city: 'Logan',
      state: 'UT',
      zip: '84567',
      phone: '************',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
    },
  ];

  public allOtherOffenders: OffenderBase[] = [
    {
      ofndrNum: 11,
      firstName: 'Yogi',
      lastName: 'Bear',
      birthDate: new Date('1980-01-01'),
      agentId: 'kchunt',
    },
    {
      ofndrNum: 12,
      firstName: 'Papa',
      lastName: 'Smurf',
      birthDate: new Date('1975-02-02'),
      agentId: 'mromney',
    },
    {
      ofndrNum: 13,
      firstName: 'Sneezy',
      lastName: 'Dwarf',
      birthDate: new Date('1985-03-03'),
      agentId: 'kchunt',
    },
    {
      ofndrNum: 14,
      firstName: 'Sleepy',
      lastName: 'Dwarf',
      birthDate: new Date('1986-04-04'),
      agentId: 'mromney',
    },
    {
      ofndrNum: 15,
      firstName: 'Smurfette',
      lastName: 'Smurf',
      birthDate: new Date('1992-05-05'),
      agentId: 'kchunt',
    },
    {
      ofndrNum: 16,
      firstName: 'Dopey',
      lastName: 'Dwarf',
      birthDate: new Date('1987-06-06'),
      agentId: 'tjones',
    },
    {
      ofndrNum: 17,
      firstName: 'Grumpy',
      lastName: 'Dwarf',
      birthDate: new Date('1988-07-07'),
      agentId: 'tjones',
    },
    {
      ofndrNum: 18,
      firstName: 'Bashful',
      lastName: 'Dwarf',
      birthDate: new Date('1989-08-08'),
      agentId: 'tjones',
    },
    {
      ofndrNum: 19,
      firstName: 'Happy',
      lastName: 'Dwarf',
      birthDate: new Date('1991-09-09'),
      agentId: 'mromney',
    },
    {
      ofndrNum: 20,
      firstName: 'Snow',
      lastName: 'White',
      birthDate: new Date('1990-01-01'),
      agentId: 'mromney',
    },
  ];

  public allOtherOffendersWithContactInfo: Offender[] = [
    {
      ofndrNum: 11,
      firstName: 'Yogi',
      lastName: 'Bear',
      agentId: 'jshardlow',
      image: '',
      address: '123 Main St.',
      city: 'Salt Lake City',
      state: 'UT',
      zip: '',
      phone: '',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
      birthDate: new Date('1980-01-01'),
    },
    {
      ofndrNum: 12,
      firstName: 'Papa',
      lastName: 'Smurf',
      agentId: 'jshardlow',
      image: '',
      address: '234 Main St.',
      city: 'Salt Lake City',
      state: 'UT',
      zip: '84101',
      phone: '************',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
      birthDate: new Date('1975-02-02'),
    },
    {
      ofndrNum: 13,
      firstName: 'Sneezy',
      lastName: 'Dwarf',
      agentId: 'jshardlow',
      image: '',
      address: '345 Center St.',
      city: 'Salt Lake City',
      state: 'UT',
      zip: '84101',
      phone: '************',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
      birthDate: new Date('1985-03-03'),
    },
    {
      ofndrNum: 14,
      firstName: 'Sleepy',
      lastName: 'Dwarf',
      agentId: 'jshardlow',
      image: '',
      address: '1234 West Temple',
      city: 'South Salt Lake City',
      state: 'UT',
      zip: '84115',
      phone: '************',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
      birthDate: new Date('1986-04-04'),
    },
    {
      ofndrNum: 15,
      firstName: 'Smurfette',
      lastName: 'Smurf',
      agentId: 'jshardlow',
      image: '',
      address: '9212 700 E.',
      city: 'Magna',
      state: 'UT',
      zip: '84111',
      phone: '************',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
      birthDate: new Date('1992-05-05'),
    },
    {
      ofndrNum: 16,
      firstName: 'Dopey',
      lastName: 'Dwarf',
      agentId: 'jshardlow',
      image: '',
      address: '67 American Ave.',
      city: 'Murray',
      state: 'UT',
      zip: '84123',
      phone: '************',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
      birthDate: new Date('1987-06-06'),
    },
    {
      ofndrNum: 17,
      firstName: 'Grumpy',
      lastName: 'Dwarf',
      agentId: 'jshardlow',
      image: '',
      address: '12324 Minuteman Dr.',
      city: 'Draper',
      state: 'UT',
      zip: '84044',
      phone: '************',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
      birthDate: new Date('1988-07-07'),
    },
    {
      ofndrNum: 18,
      firstName: 'Bashful',
      lastName: 'Dwarf',
      agentId: 'jshardlow',
      image: '',
      address: '8523 Redwood Rd.',
      city: 'South Jordan',
      state: 'UT',
      zip: '84088',
      phone: '************',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
      birthDate: new Date('1989-08-08'),
    },
    {
      ofndrNum: 19,
      firstName: 'Happy',
      lastName: 'Dwarf',
      agentId: 'jshardlow',
      image: '',
      address: '735 500W',
      city: 'Moab',
      state: 'UT',
      zip: '84452',
      phone: '************',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
      birthDate: new Date('1991-09-09'),
    },
    {
      ofndrNum: 20,
      firstName: 'Snow',
      lastName: 'White',
      agentId: 'jshardlow',
      image: '',
      address: '345 State St.',
      city: 'Logan',
      state: 'UT',
      zip: '84567',
      phone: '************',
      lastSuccessfulContactDate: new Date(),
      contactArray: [],
      birthDate: new Date('1990-01-01'),
    },
  ];

  public officerList: Agent[] = [
    {
      agentId: 'kchunt',
      firstName: 'Kurt',
      lastName: 'Hunt',
      fullName: 'Kurt Hunt',
      email: '<EMAIL>',
      image: '',
      address: '',
      city: '',
      state: '',
      zip: '',
      supervisorId: 'jshardlow',
      ofndrNumList: ([] = []),
    },
    {
      agentId: 'jshardlow',
      firstName: 'Jeff',
      lastName: 'Shardlow',
      fullName: 'Jeff Shardlow',
      email: '<EMAIL>',
      image: '',
      address: '',
      city: '',
      state: '',
      zip: '',
      supervisorId: 'kchunt',
      ofndrNumList: ([] = []),
    },
    {
      agentId: 'mromney',
      firstName: 'Matt',
      lastName: 'Romney',
      fullName: 'Matt Romney',
      email: '<EMAIL>',
      image: '',
      address: '',
      city: '',
      state: '',
      zip: '',
      supervisorId: 'jshardlow',
      ofndrNumList: ([] = []),
    },
    {
      agentId: 'tjones',
      firstName: 'Tess',
      lastName: 'Jones',
      fullName: 'Tess Jones',
      email: '<EMAIL>',
      image: '',
      address: '',
      city: '',
      state: '',
      zip: '',
      supervisorId: 'jshardlow',
      ofndrNumList: ([] = []),
    },
    {
      agentId: 'jules',
      firstName: 'Jules',
      lastName: 'Hill',
      fullName: 'Jules Hill',
      email: '<EMAIL>',
      image: '',
      address: '',
      city: '',
      state: '',
      zip: '',
      supervisorId: 'jshardlow',
      ofndrNumList: ([] = []),
    },
    {
      agentId: 'hicks',
      firstName: 'John',
      lastName: 'Hicks',
      fullName: 'John Hicks',
      email: '<EMAIL>',
      image: '',
      address: '',
      city: '',
      state: '',
      zip: '',
      supervisorId: 'jshardlow',
      ofndrNumList: ([] = []),
    },
  ];

  public contactTypeList: Select2Model[] = [
    { id: 98, text: 'ADDRESS VERIFICATION' },
    { id: 8, text: 'CASE UPDATE' },
    { id: 77, text: 'FUGITIVE UPDATE' },
    { id: 108, text: 'GPS/ELECTRONIC MONIT' },
    { id: 1, text: 'OFFICE REPORT' },
    { id: 114, text: 'PROGRESS NOTE PD' },
    { id: 2, text: 'STAFF TO OFFENDER' },
    { id: 3, text: 'TELEPHONE' },
  ];
  public locationList: Select2Model[] = [
    { id: 1, text: 'OFFICE' },
    { id: 2, text: 'RESIDENCE' },
    { id: 3, text: 'EMPLOYMENT' },
    { id: 4, text: 'COMMUNITY CORRECTION CENTER' },
    { id: 5, text: 'COMMUNITY SERVICE SITE' },
    { id: 6, text: 'COURT' },
    { id: 7, text: 'TX/RESOURCE CENTER' },
    { id: 8, text: 'OTHER RESIDENCE' },
    { id: 9, text: 'FIELD' },
    { id: 10, text: 'TREATMENT PROVIDER' },
    { id: 11, text: 'JAIL' },
    { id: 12, text: 'CRIME SCENE' },
    { id: 13, text: 'LAW ENFORCEMENT AGENCY' },
    { id: 14, text: 'OTHER' },
    { id: 15, text: 'FAMILY' },
    { id: 16, text: 'ASSOCIATE' },
    { id: 17, text: 'BOARD OF PARDONS/PAROLE' },
    { id: 18, text: 'COMPACT STATE' },
    { id: 19, text: 'VICTIM' },
    { id: 20, text: 'PRISON' },
    { id: 21, text: 'NOT APPLICABLE' },
    { id: 22, text: 'OTHER AGENCY' },
    { id: 25, text: 'EMPLOYMENT COORDINATOR' },
    { id: 26, text: 'UDOCA' },
    { id: 27, text: 'HOMELESS/OTHER SERVICES' },
  ];

  constructor() {}
}
