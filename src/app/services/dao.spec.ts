import { TestBed } from '@angular/core/testing';\n\nimport { Dao } from './dao';\nimport { Agent } from '../model/Agent';\nimport { Offender } from '../model/Offender';\nimport { Select2Model } from '../model/Select2Model';\n\ndescribe('Dao', () => {\n  let service: Dao;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      providers: [Dao]\n    });\n    service = TestBed.inject(Dao);\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  it('should have agent data', () => {\n    expect(service.agent).toBeTruthy();\n    expect(service.agent.agentId).toBeDefined();\n    expect(service.agent.firstName).toBeDefined();\n    expect(service.agent.lastName).toBeDefined();\n    expect(service.agent.fullName).toBeDefined();\n    expect(service.agent.email).toBeDefined();\n  });\n\n  it('should have caseload data', () => {\n    expect(service.myCaseload).toBeTruthy();\n    expect(Array.isArray(service.myCaseload)).toBe(true);\n    \n    if (service.myCaseload.length > 0) {\n      const offender = service.myCaseload[0];\n      expect(offender.ofndrNum).toBeDefined();\n      expect(offender.firstName).toBeDefined();\n      expect(offender.lastName).toBeDefined();\n      expect(offender.birthDate).toBeDefined();\n    }\n  });\n\n  it('should have other offenders data', () => {\n    expect(service.otherOffenders).toBeTruthy();\n    expect(Array.isArray(service.otherOffenders)).toBe(true);\n  });\n\n  it('should have location list data', () => {\n    expect(service.locationList).toBeTruthy();\n    expect(Array.isArray(service.locationList)).toBe(true);\n    \n    if (service.locationList.length > 0) {\n      const location = service.locationList[0];\n      expect(location.id).toBeDefined();\n      expect(location.text).toBeDefined();\n    }\n  });\n\n  it('should have contact type list data', () => {\n    expect(service.contactTypeList).toBeTruthy();\n    expect(Array.isArray(service.contactTypeList)).toBe(true);\n    \n    if (service.contactTypeList.length > 0) {\n      const contactType = service.contactTypeList[0];\n      expect(contactType.id).toBeDefined();\n      expect(contactType.text).toBeDefined();\n    }\n  });\n\n  it('should have officer list data', () => {\n    expect(service.officerList).toBeTruthy();\n    expect(Array.isArray(service.officerList)).toBe(true);\n    \n    if (service.officerList.length > 0) {\n      const officer = service.officerList[0];\n      expect(officer.agentId).toBeDefined();\n      expect(officer.firstName).toBeDefined();\n      expect(officer.lastName).toBeDefined();\n      expect(officer.fullName).toBeDefined();\n    }\n  });\n\n  it('should have all other offenders data', () => {\n    expect(service.allOtherOffenders).toBeTruthy();\n    expect(Array.isArray(service.allOtherOffenders)).toBe(true);\n  });\n\n  it('should provide consistent data types', () => {\n    // Verify agent is of correct type\n    expect(typeof service.agent.agentId).toBe('string');\n    expect(typeof service.agent.firstName).toBe('string');\n    expect(typeof service.agent.lastName).toBe('string');\n    expect(typeof service.agent.fullName).toBe('string');\n    expect(typeof service.agent.email).toBe('string');\n    \n    // Verify arrays contain correct types\n    service.myCaseload.forEach(offender => {\n      expect(typeof offender.ofndrNum).toBe('number');\n      expect(typeof offender.firstName).toBe('string');\n      expect(typeof offender.lastName).toBe('string');\n      expect(offender.birthDate instanceof Date).toBe(true);\n    });\n    \n    service.locationList.forEach(location => {\n      expect(typeof location.id).toBe('number');\n      expect(typeof location.text).toBe('string');\n    });\n    \n    service.contactTypeList.forEach(contactType => {\n      expect(typeof contactType.id).toBe('number');\n      expect(typeof contactType.text).toBe('string');\n    });\n  });\n});