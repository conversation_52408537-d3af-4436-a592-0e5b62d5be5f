@use "./css/light.scss";

.header-icon {
  padding: 0;
  width: 100%;
  height: 100%;
  margin-right: 1rem;
}

.input-field-icon {
  padding: 0;
  width: 100%;
  height: 100%;
}

.header-spacer {
  flex: 1 1 auto;
}

.primary-container {
  background-color: var(--md-sys-color-primary);
  font-family: "Merriweather";
  color: var(--md-sys-color-on-primary);
  padding: 0.8rem;
  width: 58px;
  height: 58px;
  border-radius: 50%;
  margin: 0.5rem;
  font-size: 1.5rem;
  font-weight: bold;
  text-align: center;
  cursor: pointer;
}

.surface {
  background: var(--md-sys-light-surface);
  color: var(--md-sys-color-on-surface);
  display: flex;
  align-self: stretch;
  padding: 32px 16px;
  flex-direction: column;
  align-items: flex-start;
}

body {
  background-color: var(--md-sys-color-surface);
  margin: 0;
}

.flex-item {
  display: flex;
  height: 48px;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
}

hr {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  align-self: stretch;
  border: 1px solid var(--md-sys-color-outline);
  border-radius: 1px;
}

.on-surface-variant {
  color: var(--md-sys-color-on-surface-variant);
  margin-top: 16px;
  font-family: "Source Sans 3";
  font-size: 13px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px;
  /* 145.455% */
  letter-spacing: 0.5px;
}

.on-surface {
  display: flex;
  font-size: 12px;
  padding: 16px;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;
  align-self: stretch;
}

.mat-mdc-card-sm-image {
  width: 81px;
  height: 81px;
  border-radius: 16px;
}

.on-background {
  color: var(--md-sys-color-on-background);
  font-family: "Source Sans 3";
  font-size: 16px;
  font-weight: 700;
  font-style: normal;
  line-height: 16px;
  /* 145.455% */
  letter-spacing: 0.5px;
}

.title_medium {
  color: var(--md-sys-light-on-background);

  /* UDC-styles/title/medium */
  font-family: "Source Sans 3";
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 32px;
  /* 150% */
  letter-spacing: 0.15px;
}

.title_small {
  color: var(--md-sys-light-on-background);

  /* UDC-styles/title/small */
  font-family: "Source Sans 3";
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 20px;
  letter-spacing: 0.1px;
  /* 142.857% */
}

.title_xlarge {
  color: var(--md-sys-light-on-background);

  /* UDC-styles/title/xlarge */
  font-family: "Source Sans 3";
  font-size: 24px;
  font-style: normal;
  font-weight: 700;
  line-height: 32px;
  /* 133.333% */
  letter-spacing: 0.5px;
}

.label_medium {
  color: var(--md-sys-light-on-background);

  /* UDC-styles/label/medium */
  font-family: "Source Sans 3";
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px;
  /* 133.333% */
  letter-spacing: 0.5px;
}

.body_small {
  color: var(--md-sys-light-on-surface);

  /* UDC-styles/body/small */
  font-family: "Source Sans 3";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  /* 133.333% */
  letter-spacing: 0.4px;
}

// .body_large {
//     color: var(--md-sys-light-on-surface);

//     /* UDC-styles/body/large */
//     font-family: "Source Sans 3";
//     font-size: 14px;
//     font-style: normal;
//     font-weight: 400;
//     line-height: 20px;
//     /* 142.857% */
//     letter-spacing: 0.4px;
// }

.headline_small {
  color: var(--md-sys-light-on-surface);

  /* UDC-styles/headline/small */
  font-size: 24px;
  font-family: "Merriweather", serif;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;
  /* 133.333% */
  letter-spacing: 0.5px;
}

.offender-card {
  margin-top: 16px;
  width: 100%;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  align-self: stretch;
  border-radius: 8px;
  background-color: var(--md-sys-color-surface);
  color: var(--md-sys-color-on-surface);
  padding: 16px;
}

.offender-card img {
  width: 81px;
  height: 81px;
  border-radius: 8px;
  flex-shrink: 0;
  background: url(../assets/images/placeholder-image.png) lightgray 50% / cover
    no-repeat;
}

.title_large {
  color: var(--md-sys-light-on-surface-variant);

  /* UDC-styles/title/large */
  font-family: "Source Sans 3";
  font-size: 22px;
  font-style: normal;
  font-weight: 400;
  line-height: 28px;
  /* 127.273% */
}

.detail-header {
  display: flex;
  padding: 32px 16px;
  align-items: flex-start;
  align-self: stretch;
}

.detail-card {
  width: 100%;
  padding: 32px 16px;
  align-items: flex-start;
  gap: 16px;
  align-self: stretch;
  border-radius: 16px;
  background: var(--md-sys-color-surface-container-low);
}

.detail-card img {
  width: 81px;
  height: 81px;
  border-radius: 8px;
  flex-shrink: 0;
  background: url(../assets/images/placeholder-image.png) lightgray 50% / cover
    no-repeat;
}

.label_medium {
  color: var(--md-sys-light-on-surface-variant);

  /* UDC-styles/label/medium */
  font-family: "Source Sans 3";
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px;
  /* 133.333% */
  letter-spacing: 0.5px;
}

.label_large {
  color: var(--md-sys-light-on-primary);
  text-align: center;
  /* UDC-styles/label/large */
  font-family: "Source Sans 3";
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
  /* 142.857% */
  letter-spacing: 0.1px;
}

.label_large_prominent {
  color: var(--md-sys-light-on-background);

  /* UDC-styles/label/large - prominent */
  font-family: var(--Label-Large-Font, "Source Sans 3");
  font-size: var(--Label-Large-Size, 14px);
  font-style: normal;
  font-weight: 700;
  line-height: var(--Label-Large-Line-Height, 20px);
  /* 142.857% */
  letter-spacing: var(--Label-Large-Tracking, 0.1px);
}

.body_large {
  color: var(--md-sys-light-on-surface);

  /* UDC-styles/body/large */
  font-family: "Source Sans 3";
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  /* 150% */
  letter-spacing: 0.5px;
}

.body_medium {
  color: var(--UDC-styles-sys-light-on-surface-variant, #49473f);

  /* UDC-styles/body/medium */
  font-family: "Source Sans 3";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  /* 142.857% */
  letter-spacing: 0.25px;
}

.ripple-container {
  cursor: pointer;

  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;

  -webkit-user-drag: none;
  -webkit-tap-highlight-color: transparent;
}

.state-icon {
  display: flex;
  padding: 0px 0px 3px 0px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  margin-bottom: 3px;
  margin-top: -3px;
}

.month_label {
  display: flex;
  padding: 18px 16px 0px 16px;
  align-items: center;
  align-self: stretch;
}

.note-icon {
  display: flex;
  padding: 0px 0px 3px 0px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  margin-bottom: 3px;
  margin-top: -3px;
}

.visit_listing_card {
  display: flex;
  padding: 12px 16px;
  align-items: flex-start;
  gap: 16px;
  align-self: stretch;
}

.contact_form_input_field {
  align-items: flex-start;
  margin-top: 14px;
  width: 100%;
}

.mat-calendar {
  background: var(--Schemes-Surface-Container-High, #ebe7e6);
}
.mat-timepicker-panel {
  background: var(--Schemes-Surface-Container-High, #ebe7e6);
}

section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  align-self: stretch;
  padding-top: 16px;
  width: 100%;
}

.button-row {
  display: flex;
  align-self: stretch;
  width: 100%;
  justify-content: flex-end;
  margin: 8px 8px 8px 0;
  flex-direction: row;
}

.mat-mdc-button-base {
  display: inline-flex;
  height: 40px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  border-radius: 100px;
}
.info-field-icon {
  color: var(--md-sys-color-primary);
  font-size: 0.9rem;
  vertical-align: top;
}
