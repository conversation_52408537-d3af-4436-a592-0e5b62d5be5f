// Test setup file for SupContact application\n\nimport 'zone.js/testing';\nimport { getTestBed } from '@angular/core/testing';\nimport {\n  BrowserDynamicTestingModule,\n  platformBrowserDynamicTesting\n} from '@angular/platform-browser-dynamic/testing';\n\n// Initialize the Angular testing environment\ngetTestBed().initTestEnvironment(\n  BrowserDynamicTestingModule,\n  platformBrowserDynamicTesting()\n);\n\n// Global test utilities and mocks\n\n// Mock IndexedDB for testing\n(global as any).indexedDB = {\n  open: jasmine.createSpy('open').and.returnValue({\n    onsuccess: null,\n    onerror: null,\n    result: {\n      createObjectStore: jasmine.createSpy('createObjectStore'),\n      transaction: jasmine.createSpy('transaction')\n    }\n  }),\n  deleteDatabase: jasmine.createSpy('deleteDatabase')\n};\n\n// Mock localStorage\n(global as any).localStorage = {\n  getItem: jasmine.createSpy('getItem'),\n  setItem: jasmine.createSpy('setItem'),\n  removeItem: jasmine.createSpy('removeItem'),\n  clear: jasmine.createSpy('clear')\n};\n\n// Mock sessionStorage\n(global as any).sessionStorage = {\n  getItem: jasmine.createSpy('getItem'),\n  setItem: jasmine.createSpy('setItem'),\n  removeItem: jasmine.createSpy('removeItem'),\n  clear: jasmine.createSpy('clear')\n};\n\n// Mock window.matchMedia for responsive design tests\n(global as any).matchMedia = jasmine.createSpy('matchMedia').and.returnValue({\n  matches: false,\n  addListener: jasmine.createSpy('addListener'),\n  removeListener: jasmine.createSpy('removeListener')\n});\n\n// Mock IntersectionObserver\n(global as any).IntersectionObserver = class {\n  observe = jasmine.createSpy('observe');\n  disconnect = jasmine.createSpy('disconnect');\n  unobserve = jasmine.createSpy('unobserve');\n};\n\n// Mock ResizeObserver\n(global as any).ResizeObserver = class {\n  observe = jasmine.createSpy('observe');\n  disconnect = jasmine.createSpy('disconnect');\n  unobserve = jasmine.createSpy('unobserve');\n};\n\n// Suppress console warnings in tests\nconst originalWarn = console.warn;\nconsole.warn = (...args: any[]) => {\n  if (args[0]?.includes && (\n    args[0].includes('Angular Material') ||\n    args[0].includes('CDK')\n  )) {\n    return;\n  }\n  originalWarn.apply(console, args);\n};