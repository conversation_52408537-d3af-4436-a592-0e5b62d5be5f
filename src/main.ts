import { bootstrapApplication } from '@angular/platform-browser';
import { appConfig } from './app/app.config';
import { App } from './app/app';

bootstrapApplication(App, appConfig)
  .catch((err) => console.error(err));

  if ('serviceWorker' in navigator) {
    window.addEventListener('load', function () {
      navigator.serviceWorker.register('/service-worker.js').then(
        function (registration) {
          console.log(
            'ServiceWorker registration successful with scope: ',
            registration.scope
          );
        },
        function (err) {
          console.log('ServiceWorker registration failed: ', err);
        }
      );
    });
  }

