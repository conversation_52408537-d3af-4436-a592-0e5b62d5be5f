// This file was generated by running 'ng generate @angular/material:theme-color'.
// Proceed with caution if making changes to this file.

@use 'sass:map';
@use '@angular/material' as mat;

// Note: Color palettes are generated from primary: #001624, secondary: #54606A, tertiary: #7C5800, error: #B7211F
$_palettes: (
  primary: (
    0: #000000,
    10: #061e2c,
    20: #1d3342,
    25: #293e4d,
    30: #344959,
    35: #405565,
    40: #4c6171,
    50: #647a8b,
    60: #7e93a5,
    70: #98aec0,
    80: #b3c9dc,
    90: #cfe5f9,
    95: #e5f2ff,
    98: #f6faff,
    99: #fcfcff,
    100: #ffffff,
  ),
  secondary: (
    0: #000000,
    10: #111d25,
    20: #26323b,
    25: #313d46,
    30: #3c4852,
    35: #48545e,
    40: #54606a,
    50: #6c7983,
    60: #86929d,
    70: #a0adb8,
    80: #bcc8d4,
    90: #d8e4f0,
    95: #e6f2fe,
    98: #f6faff,
    99: #fbfcff,
    100: #ffffff,
  ),
  tertiary: (
    0: #000000,
    10: #271900,
    20: #412d00,
    25: #4f3700,
    30: #5e4200,
    35: #6d4d00,
    40: #7c5800,
    50: #98701d,
    60: #b48a36,
    70: #d2a44d,
    80: #efbf65,
    90: #ffdea7,
    95: #ffeed7,
    98: #fff8f3,
    99: #fffbff,
    100: #ffffff,
  ),
  neutral: (
    0: #000000,
    10: #1b1c1d,
    20: #303032,
    25: #3b3b3d,
    30: #464748,
    35: #525254,
    40: #5e5e5f,
    50: #777778,
    60: #919092,
    70: #acabac,
    80: #c7c6c7,
    90: #e3e2e3,
    95: #f2f0f1,
    98: #fbf9fa,
    99: #fdfcfd,
    100: #ffffff,
    4: #0d0e0f,
    6: #121315,
    12: #1f2021,
    17: #292a2b,
    22: #343536,
    24: #38393a,
    87: #dbd9db,
    92: #e9e8e9,
    94: #efedef,
    96: #f5f3f4,
  ),
  neutral-variant: (
    0: #000000,
    10: #181c20,
    20: #2c3135,
    25: #373c40,
    30: #43474c,
    35: #4e5357,
    40: #5a5f63,
    50: #73777c,
    60: #8d9196,
    70: #a8acb1,
    80: #c3c7cc,
    90: #dfe3e8,
    95: #edf1f6,
    98: #f6faff,
    99: #fcfcff,
    100: #ffffff,
  ),
  error: (
    0: #000000,
    10: #410002,
    20: #690005,
    25: #7e0008,
    30: #93000b,
    35: #a51114,
    40: #b7211f,
    50: #da3c34,
    60: #fe554a,
    70: #ff897d,
    80: #ffb4ab,
    90: #ffdad6,
    95: #ffedea,
    98: #fff8f7,
    99: #fffbff,
    100: #ffffff,
  ),
);

$_rest: (
  secondary: map.get($_palettes, secondary),
  neutral: map.get($_palettes, neutral),
  neutral-variant: map.get($_palettes,  neutral-variant),
  error: map.get($_palettes, error),
);

$primary-palette: map.merge(map.get($_palettes, primary), $_rest);
$tertiary-palette: map.merge(map.get($_palettes, tertiary), $_rest);