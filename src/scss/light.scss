@import '~@angular/material/theming';

@include mat.core();

$light-theme-primary: mat.define-palette(mat.$indigo-palette, 500, 100, 700);
$light-theme-accent: mat.define-palette(mat.$pink-palette, A200, A100, A400);
$light-theme-warn: mat.define-palette(mat.$red-palette);

$light-theme: mat.define-light-theme((
  color: (
    primary: $light-theme-primary,
    accent: $light-theme-accent,
    warn: $light-theme-warn,
  ),
));

@include mat.all-component-themes($light-theme);