{"$schema": "./node_modules/@angular/service-worker/config/schema.json", "index": "/index.html", "assetGroups": [{"name": "app", "installMode": "prefetch", "resources": {"files": ["/favicon.ico", "/index.html", "/manifest.webmanifest", "/*.css", "/*.js"]}}, {"name": "assets", "installMode": "prefetch", "updateMode": "prefetch", "resources": {"files": ["/**/*.(svg|cur|jpg|jpeg|png|apng|webp|avif|gif|otf|ttf|woff|woff2)"]}}, {"name": "ut-udc-apnp-field_contact/assets/icons", "installMode": "prefetch", "updateMode": "prefetch", "resources": {"files": ["/**/*.(ico|cur|svg)"]}}, {"name": "styles", "installMode": "lazy", "updateMode": "prefetch", "resources": {"files": ["/**/*.(css|scss)"]}}, {"name": "scripts", "installMode": "lazy", "updateMode": "prefetch", "resources": {"files": ["/**/*.(js)"]}}, {"name": "ut-udc-apnp-field_contact/assets/images", "installMode": "prefetch", "updateMode": "prefetch", "resources": {"files": ["/**/*.(jpg|jpeg|png|apng|webp|avif|gif)"]}}, {"name": "data", "installMode": "prefetch", "updateMode": "prefetch", "resources": {"files": ["/**/*.(json)"]}}], "dataGroups": [{"name": "api", "urls": ["**/api/**"], "cacheQueryOptions": {"ignoreVary": true, "ignoreSearch": true}, "cacheConfig": {"maxSize": 100, "maxAge": "4h", "timeout": "3s", "strategy": "performance"}}]}