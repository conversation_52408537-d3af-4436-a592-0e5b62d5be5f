{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"ut-udc-apnp-field_contact": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"outputPath": "dist/ut-udc-apnp-field_contact", "index": "src/index.html", "browser": "src/main.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}, {"input": "src/assets/icons", "output": "assets/icons", "glob": "**/*"}, "src/manifest.webmanifest", "src/service-worker.js", "src/ngsw-worker.js"], "styles": [{"input": "src/scss/scss_theme-colors.scss"}, "node_modules/bootstrap/dist/css/bootstrap.min.css", "src/styles.scss"], "scripts": ["node_modules/jquery/dist/jquery.min.js", "node_modules/bootstrap/dist/js/bootstrap.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2MB", "maximumError": "5MB"}, {"type": "anyComponentStyle", "maximumWarning": "2MB", "maximumError": "5MB"}], "outputHashing": "all", "serviceWorker": "ngsw-config.json"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}]}, "staging": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}]}, "production-no-opt": {"optimization": false, "outputHashing": "none", "sourceMap": true, "namedChunks": true, "aot": true, "extractLicenses": false, "buildOptimizer": false, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.production-no-opt.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "ut-udc-apnp-field_contact:build:production"}, "development": {"buildTarget": "ut-udc-apnp-field_contact:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n"}, "test": {"builder": "@angular/build:karma", "options": {"tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}, "/assets"], "styles": ["src/styles.scss"]}}}}}}